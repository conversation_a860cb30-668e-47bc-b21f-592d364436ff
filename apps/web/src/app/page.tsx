'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Head<PERSON> } from '@/components/layout/Header'
import { SportsGrid } from '@/components/sports/SportsGrid'
import { LiveEvents } from '@/components/sports/LiveEvents'
import { BettingSlip } from '@/components/betting/BettingSlip'
import { CasinoLobby } from '@/components/casino/CasinoLobby'
import { StatsOverview } from '@/components/dashboard/StatsOverview'
import { TrendingBets } from '@/components/sports/TrendingBets'
import { AnimatedHero } from '@/components/hero/AnimatedHero'
import { LoginForm } from '@/components/auth/LoginForm'
import { useAuth } from '@/store/auth-store'

export default function HomePage() {
  const [activeTab, setActiveTab] = useState<'sports' | 'casino' | 'live'>('sports')
  const { isAuthenticated, isLoading } = useAuth()

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-white text-xl">Loading...</div>
        </div>
      </div>
    )
  }

  // Show login form if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950">
        <Header />
        <main className="w-full min-w-0">
          <div className="w-full px-4 lg:px-6 xl:px-8 2xl:px-12 py-6 lg:py-8">
            <div className="max-w-md mx-auto mt-20">
              <LoginForm />
            </div>
          </div>
        </main>
      </div>
    )
  }

  // Show main dashboard for authenticated users
  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <main className="w-full min-w-0">
          <div className="w-full px-4 lg:px-6 xl:px-8 2xl:px-12 py-6 lg:py-8">
            {/* Hero Section */}
            <div className="mb-8">
              <AnimatedHero />
            </div>

            {/* Stats Overview */}
            <StatsOverview />

            {/* Navigation Tabs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <div className="flex space-x-1 bg-dark-800 p-1 rounded-xl w-full max-w-md mx-auto lg:mx-0 lg:max-w-fit">
                {[
                  { id: 'sports', label: 'Sports Betting', icon: '⚽' },
                  { id: 'live', label: 'Live Events', icon: '🔴' },
                  { id: 'casino', label: 'Casino Games', icon: '🎰' },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-primary-600 text-white shadow-lg'
                        : 'text-gray-400 hover:text-white hover:bg-dark-700'
                    }`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="hidden sm:inline">{tab.label}</span>
                  </button>
                ))}
              </div>
            </motion.div>

            {/* Content based on active tab */}
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="w-full"
            >
              {activeTab === 'sports' && (
                <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 lg:gap-6 items-start">
                  <div className="lg:col-span-2 xl:col-span-3 2xl:col-span-4">
                    <SportsGrid />
                  </div>
                  <div className="lg:col-span-1 xl:col-span-1 2xl:col-span-1">
                    <TrendingBets />
                  </div>
                </div>
              )}

              {activeTab === 'live' && <LiveEvents />}

              {activeTab === 'casino' && <CasinoLobby />}
            </motion.div>
          </div>
        </main>

        {/* Betting Slip - Fixed position */}
        <BettingSlip />
      </div>
    )
}
