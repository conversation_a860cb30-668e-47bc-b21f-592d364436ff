'use client'

import { motion } from 'framer-motion'
import { useAuth } from '@/store/auth-store'
import { CompactLoginForm } from '@/components/auth/CompactLoginForm'
import { UserDashboard } from '@/components/dashboard/UserDashboard'

export function AnimatedHero() {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-transparent border-t-primary-500 rounded-full"
        />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="relative min-h-[400px] flex items-center justify-center py-8 lg:py-12"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-secondary-500/5" />
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary-500/10 rounded-full blur-3xl" />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        {isAuthenticated ? (
          <UserDashboard />
        ) : (
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
            {/* Left Side - Welcome Message */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="flex-1 text-center lg:text-left"
            >
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-3xl lg:text-5xl xl:text-6xl font-bold mb-4 leading-tight"
              >
                <span className="text-white">Welcome to</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500">
                  Kesar Mango
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg lg:text-xl text-gray-300 mb-6 max-w-xl"
              >
                Explore live sports betting, casino games, and more. Login to unlock personalized features and track your bets.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col sm:flex-row gap-3 justify-center lg:justify-start"
              >
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm">Live Events</span>
                  </div>
                </div>
                <div className="bg-dark-800/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-dark-700">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                    <span className="text-gray-300 text-sm">Instant Payouts</span>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Right Side - Compact Login Form */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="flex-shrink-0 w-full max-w-sm"
            >
              <div className="bg-dark-800/80 backdrop-blur-md rounded-xl p-6 border border-dark-700 shadow-2xl">
                <CompactLoginForm />
              </div>
            </motion.div>
          </div>
        )}
      </div>
    </motion.div>
  )
}
