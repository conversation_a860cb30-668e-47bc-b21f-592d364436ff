'use client'

import { motion } from 'framer-motion'
import { useAuth } from '@/store/auth-store'
import { CompactLoginForm } from '@/components/auth/CompactLoginForm'
import { UserDashboard } from '@/components/dashboard/UserDashboard'

export function AnimatedHero() {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-transparent border-t-primary-500 rounded-full"
        />
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="relative min-h-[300px] flex items-center justify-center py-6 lg:py-8"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-secondary-500/5" />
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary-500/10 rounded-full blur-3xl" />
      </div>

      {/* Content */}
      <div className="relative z-10 w-full">
        {isAuthenticated ? (
          <UserDashboard />
        ) : (
          /* Single Unified Container */
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="w-full max-w-6xl mx-auto bg-dark-800/60 backdrop-blur-md rounded-2xl border border-dark-700 shadow-2xl overflow-hidden"
          >
            <div className="flex flex-col lg:flex-row">
              {/* Left Side - Welcome Message */}
              <div className="flex-1 p-6 lg:p-8">
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="text-2xl lg:text-3xl xl:text-4xl font-bold mb-3 leading-tight"
                >
                  <span className="text-white">Welcome to</span>{' '}
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500">
                    Kesar Mango
                  </span>
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="text-base lg:text-lg text-gray-300 mb-4 max-w-lg"
                >
                  Explore live sports betting, casino games, and more. Login to unlock personalized features and track your bets.
                </motion.p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="flex flex-wrap gap-3"
                >
                  <div className="bg-dark-700/50 backdrop-blur-sm rounded-lg px-3 py-2 border border-dark-600">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                      <span className="text-gray-300 text-sm">Live Events</span>
                    </div>
                  </div>
                  <div className="bg-dark-700/50 backdrop-blur-sm rounded-lg px-3 py-2 border border-dark-600">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                      <span className="text-gray-300 text-sm">Instant Payouts</span>
                    </div>
                  </div>
                  <div className="bg-dark-700/50 backdrop-blur-sm rounded-lg px-3 py-2 border border-dark-600">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" />
                      <span className="text-gray-300 text-sm">24/7 Support</span>
                    </div>
                  </div>
                </motion.div>
              </div>

              {/* Right Side - Integrated Login Form */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="flex-shrink-0 w-full lg:w-96 bg-dark-900/50 border-l border-dark-700 lg:border-l lg:border-t-0 border-t"
              >
                <div className="p-6 lg:p-8">
                  <CompactLoginForm />
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
        )}
      </div>
    </motion.div>
  )
}
