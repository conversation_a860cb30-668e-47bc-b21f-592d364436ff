'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FireIcon, ClockIcon } from '@heroicons/react/24/outline'
import { mockEvents } from '@/data/mock-data'
import { useBetting } from '@/store/betting-store'
import { BetSelection } from '@/types'

export function LiveEvents() {
  const [mounted, setMounted] = useState(false)
  const { addSelection } = useBetting()

  useEffect(() => {
    setMounted(true)
  }, [])

  const liveEvents = mockEvents.filter(event => event.isLive)

  const handleOddsClick = (event: any, market: any, outcome: any) => {
    const selection: BetSelection = {
      outcomeId: outcome.id,
      eventId: event.id,
      marketId: market.id,
      eventName: `${event.homeTeam.name} vs ${event.awayTeam.name}`,
      marketName: market.name,
      outcomeName: outcome.name,
      odds: outcome.odds,
      isLive: event.isLive,
    }
    addSelection(selection)
  }

  return (
    <div className="betting-section-spacing">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <FireIcon className="h-6 w-6 text-red-400" />
        </div>
        <h2 className="text-2xl font-bold text-white">Live Events</h2>
        <span className="bg-red-500 text-white text-sm px-3 py-1 rounded-full">
          {liveEvents.length} Live
        </span>
      </div>

      {/* Live Events Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {liveEvents.map((event, index) => (
          <motion.div
            key={event.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="bg-gradient-to-br from-dark-800 to-dark-900 rounded-xl border border-red-500/30 overflow-hidden"
          >
            {/* Live Header */}
            <div className="bg-red-500/10 border-b border-red-500/30 p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-red-400 font-semibold text-sm">LIVE</span>
                </div>
                <div className="text-white font-bold">
                  {event.minute}&apos; {event.period}
                </div>
              </div>
            </div>

            {/* Match Info */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-6">
                <div className="flex-1 space-y-3">
                  {/* Home Team */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-white">
                          {event.homeTeam.name.charAt(0)}
                        </span>
                      </div>
                      <span className="text-white font-medium">{event.homeTeam.name}</span>
                    </div>
                    <span className="text-3xl font-bold text-white">
                      {event.homeScore}
                    </span>
                  </div>

                  {/* Away Team */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-white">
                          {event.awayTeam.name.charAt(0)}
                        </span>
                      </div>
                      <span className="text-white font-medium">{event.awayTeam.name}</span>
                    </div>
                    <span className="text-3xl font-bold text-white">
                      {event.awayScore}
                    </span>
                  </div>
                </div>
              </div>

              {/* Live Markets */}
              <div className="space-y-4">
                {event.markets.map((market) => (
                  <div key={market.id}>
                    <h4 className="text-gray-400 text-sm font-medium mb-3">
                      {market.name}
                    </h4>
                    <div className="grid grid-cols-3 gap-2">
                      {market.outcomes.map((outcome) => (
                        <motion.button
                          key={outcome.id}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleOddsClick(event, market, outcome)}
                          className="bg-dark-700 hover:bg-dark-600 border border-dark-600 hover:border-primary-500 rounded-lg p-3 transition-all duration-200 group"
                        >
                          <div className="text-white font-medium text-sm mb-1 group-hover:text-primary-400">
                            {outcome.name}
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-primary-400 font-bold text-lg">
                              {outcome.odds.toFixed(2)}
                            </span>
                            {outcome.trend && outcome.trend !== 'neutral' && (
                              <motion.span
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className={`text-sm ${
                                  outcome.trend === 'up' ? 'text-green-400' : 'text-red-400'
                                }`}
                              >
                                {outcome.trend === 'up' ? '↗' : '↘'}
                              </motion.span>
                            )}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {liveEvents.length === 0 && (
        <div className="text-center py-12">
          <ClockIcon className="h-16 w-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No Live Events</h3>
          <p className="text-gray-400">
            Check back soon for live betting opportunities
          </p>
        </div>
      )}
    </div>
  )
}
