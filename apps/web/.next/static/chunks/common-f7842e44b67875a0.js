"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[592],{8565:function(e,t,n){n.d(t,{H:function(){return i},a:function(){return r}});var a=n(7573),s=n(7653);let o=(0,s.createContext)(void 0),r=()=>{let e=(0,s.useContext)(o);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},l={id:"1",username:"BetMaster",email:"<EMAIL>",balance:0,currency:"USD",avatar:void 0,isVerified:!0,createdAt:"2023-01-15T00:00:00.000Z"};function i(e){let{children:t}=e,[n,r]=(0,s.useState)(null),[i,u]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(()=>{let e=localStorage.getItem("kesar_mango_user");if(e)try{r(JSON.parse(e))}catch(e){localStorage.removeItem("kesar_mango_user")}u(!1)})()},[]);let c=async(e,t)=>{if(u(!0),await new Promise(e=>setTimeout(e,1e3)),e&&t.length>=6){let t={...l,email:e};return r(t),localStorage.setItem("kesar_mango_user",JSON.stringify(t)),u(!1),!0}return u(!1),!1};return(0,a.jsx)(o.Provider,{value:{user:n,isAuthenticated:!!n,isLoading:i,login:c,logout:()=>{r(null),localStorage.removeItem("kesar_mango_user")},updateBalance:e=>{if(n){let t={...n,balance:e};r(t),localStorage.setItem("kesar_mango_user",JSON.stringify(t))}}},children:t})}},419:function(e,t,n){n.d(t,{H:function(){return c},I:function(){return d}});var a=n(7573),s=n(7653);let o={isOpen:!1,selections:[],stake:10,betType:"single",totalOdds:1,potentialWin:0},r=(e,t)=>{if(0===e.length)return 1;if("single"===t){var n;return(null===(n=e[0])||void 0===n?void 0:n.odds)||1}return"combo"===t?e.reduce((e,t)=>e*t.odds,1):e.reduce((e,t)=>e*t.odds,1)},l=(e,t)=>e*t;function i(e,t){switch(t.type){case"ADD_SELECTION":{let n;let a=e.selections.findIndex(e=>e.outcomeId===t.payload.outcomeId);n=a>=0?e.selections.map((e,n)=>n===a?t.payload:e):[...e.selections,t.payload];let s=r(n,e.betType),o=l(e.stake,s);return{...e,selections:n,totalOdds:s,potentialWin:o,isOpen:!0}}case"REMOVE_SELECTION":{let n=e.selections.filter(e=>e.outcomeId!==t.payload),a=r(n,e.betType),s=l(e.stake,a);return{...e,selections:n,totalOdds:a,potentialWin:s,isOpen:n.length>0&&e.isOpen}}case"CLEAR_SELECTIONS":return{...e,selections:[],totalOdds:1,potentialWin:0};case"SET_STAKE":{let n=l(t.payload,e.totalOdds);return{...e,stake:t.payload,potentialWin:n}}case"SET_BET_TYPE":{let n=r(e.selections,t.payload),a=l(e.stake,n);return{...e,betType:t.payload,totalOdds:n,potentialWin:a}}case"TOGGLE_SLIP":return{...e,isOpen:!e.isOpen};case"OPEN_SLIP":return{...e,isOpen:!0};case"CLOSE_SLIP":return{...e,isOpen:!1};case"UPDATE_ODDS":{let n=e.selections.map(e=>e.outcomeId===t.payload.outcomeId?{...e,odds:t.payload.odds}:e),a=r(n,e.betType),s=l(e.stake,a);return{...e,selections:n,totalOdds:a,potentialWin:s}}default:return e}}let u=(0,s.createContext)(null);function c(e){let{children:t}=e,[n,r]=(0,s.useReducer)(i,o);return(0,a.jsx)(u.Provider,{value:{state:n,dispatch:r},children:t})}function d(){let e=(0,s.useContext)(u);if(!e)throw Error("useBetting must be used within a BettingProvider");let{state:t,dispatch:n}=e;return{...t,addSelection:e=>n({type:"ADD_SELECTION",payload:e}),removeSelection:e=>n({type:"REMOVE_SELECTION",payload:e}),clearSelections:()=>n({type:"CLEAR_SELECTIONS"}),setStake:e=>n({type:"SET_STAKE",payload:e}),setBetType:e=>n({type:"SET_BET_TYPE",payload:e}),toggleSlip:()=>n({type:"TOGGLE_SLIP"}),openSlip:()=>n({type:"OPEN_SLIP"}),closeSlip:()=>n({type:"CLOSE_SLIP"}),updateOdds:(e,t)=>n({type:"UPDATE_ODDS",payload:{outcomeId:e,odds:t}}),hasSelections:t.selections.length>0,selectionCount:t.selections.length,canPlaceBet:t.selections.length>0&&t.stake>0}}}}]);