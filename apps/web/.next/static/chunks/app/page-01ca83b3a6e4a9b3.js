(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{2421:function(e,s,a){Promise.resolve().then(a.bind(a,9874))},9874:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return et}});var t=a(7573),i=a(7653),l=a(5021),r=a(9760),n=a(3555),d=a(1861),c=a(8172),o=a(7007),m=a(419),x=a(8565);function h(e){let{onMenuClick:s}=e,[a,h]=(0,i.useState)(""),{selectionCount:p,toggleSlip:u}=(0,m.I)(),{user:g,isAuthenticated:b,logout:j}=(0,x.a)();return(0,t.jsxs)("header",{className:"sticky top-0 z-50 bg-dark-900/95 backdrop-blur-md border-b border-dark-700 w-full",children:[(0,t.jsx)("div",{className:"w-full px-4 lg:px-6 xl:px-8 2xl:px-12",children:(0,t.jsxs)("div",{className:"flex items-center justify-between h-16 w-full",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"K"})}),(0,t.jsx)("span",{className:"hidden sm:block text-xl font-bold text-white",children:"Kesar Mango"})]})}),(0,t.jsx)("div",{className:"hidden md:flex flex-1 max-w-sm lg:max-w-md xl:max-w-lg 2xl:max-w-xl mx-4 lg:mx-8",children:(0,t.jsxs)("div",{className:"relative w-full",children:[(0,t.jsx)(r.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,t.jsx)("input",{type:"text",placeholder:"Search events, teams, or games...",value:a,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[b&&g&&(0,t.jsxs)(l.E.div,{whileHover:{scale:1.05},className:"hidden sm:flex items-center space-x-2 bg-dark-800 px-4 py-2 rounded-lg border border-dark-600",children:[(0,t.jsx)(n.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-sm text-gray-400",children:"Balance"}),(0,t.jsxs)("div",{className:"font-semibold text-white",children:["$",g.balance.toFixed(2)]})]})]}),(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:u,className:"relative bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors",children:[(0,t.jsx)("span",{className:"hidden sm:inline",children:"Bet Slip"}),(0,t.jsx)("span",{className:"sm:hidden",children:"Slip"}),p>0&&(0,t.jsx)(l.E.span,{initial:{scale:0},animate:{scale:1},className:"absolute -top-2 -right-2 bg-secondary-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold",children:p})]}),(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"relative p-2 rounded-lg hover:bg-dark-700 transition-colors",children:[(0,t.jsx)(d.Z,{className:"h-6 w-6 text-gray-300"}),(0,t.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"})]}),(0,t.jsx)("div",{className:"relative",children:b&&g?(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600",children:[(0,t.jsx)(n.Z,{className:"h-4 w-4 text-green-400"}),(0,t.jsxs)("span",{className:"text-green-400 font-medium text-sm",children:["$",g.balance.toFixed(2)]})]}),(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 p-2 rounded-lg hover:bg-dark-700 transition-colors",children:[g.avatar?(0,t.jsx)("img",{src:g.avatar,alt:g.username,className:"h-8 w-8 rounded-full"}):(0,t.jsx)(c.Z,{className:"h-8 w-8 text-gray-300"}),(0,t.jsx)("span",{className:"hidden lg:block text-white font-medium",children:g.username})]}),(0,t.jsx)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:j,className:"p-2 rounded-lg hover:bg-red-600/20 transition-colors group",title:"Sign Out",children:(0,t.jsx)(o.Z,{className:"h-5 w-5 text-gray-400 group-hover:text-red-400"})})]}):(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{let e=document.querySelector("main");null==e||e.scrollIntoView({behavior:"smooth"})},className:"flex items-center space-x-2 bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-4 py-2 rounded-lg font-medium hover:from-primary-700 hover:to-secondary-700 transition-all",children:[(0,t.jsx)(c.Z,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Sign In"})]})})]})]})}),(0,t.jsx)("div",{className:"md:hidden px-4 pb-4",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(r.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,t.jsx)("input",{type:"text",placeholder:"Search events, teams, or games...",value:a,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-dark-800 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})})]})}var p=a(7706);let u=[{id:"1",leagueId:"1",homeTeam:{id:"1",name:"Manchester United",logo:"/teams/man-utd.png"},awayTeam:{id:"2",name:"Liverpool",logo:"/teams/liverpool.png"},startTime:"2024-01-15T20:00:00.000Z",status:"scheduled",isLive:!1,markets:[{id:"1",eventId:"1",name:"Match Winner",type:"match_winner",isActive:!0,isSuspended:!1,outcomes:[{id:"1",marketId:"1",name:"Manchester United",odds:2.45,isActive:!0,trend:"up"},{id:"2",marketId:"1",name:"Draw",odds:3.2,isActive:!0,trend:"neutral"},{id:"3",marketId:"1",name:"Liverpool",odds:2.8,isActive:!0,trend:"down"}]},{id:"2",eventId:"1",name:"Total Goals",type:"over_under",isActive:!0,isSuspended:!1,outcomes:[{id:"4",marketId:"2",name:"Over 2.5",odds:1.85,isActive:!0,trend:"up"},{id:"5",marketId:"2",name:"Under 2.5",odds:1.95,isActive:!0,trend:"down"}]}]},{id:"2",leagueId:"1",homeTeam:{id:"3",name:"Chelsea",logo:"/teams/chelsea.png"},awayTeam:{id:"4",name:"Arsenal",logo:"/teams/arsenal.png"},startTime:"2024-01-15T18:00:00.000Z",status:"live",isLive:!0,homeScore:1,awayScore:2,minute:67,period:"2nd Half",markets:[{id:"3",eventId:"2",name:"Match Winner",type:"match_winner",isActive:!0,isSuspended:!1,outcomes:[{id:"6",marketId:"3",name:"Chelsea",odds:4.5,isActive:!0,trend:"up"},{id:"7",marketId:"3",name:"Draw",odds:3.8,isActive:!0,trend:"neutral"},{id:"8",marketId:"3",name:"Arsenal",odds:1.65,isActive:!0,trend:"down"}]}]},{id:"3",leagueId:"2",homeTeam:{id:"5",name:"Lakers",logo:"/teams/lakers.png"},awayTeam:{id:"6",name:"Warriors",logo:"/teams/warriors.png"},startTime:"2024-01-15T22:00:00.000Z",status:"scheduled",isLive:!1,markets:[{id:"4",eventId:"3",name:"Match Winner",type:"match_winner",isActive:!0,isSuspended:!1,outcomes:[{id:"9",marketId:"4",name:"Lakers",odds:1.95,isActive:!0,trend:"neutral"},{id:"10",marketId:"4",name:"Warriors",odds:1.85,isActive:!0,trend:"up"}]}]}],g=[{id:"1",name:"Mega Moolah",provider:"Microgaming",category:"Slots",thumbnail:"/games/mega-moolah.jpg",isLive:!1,minBet:.25,maxBet:6.25,rtp:88.12,popularity:95,isNew:!1,isFeatured:!0},{id:"2",name:"Live Blackjack",provider:"Evolution Gaming",category:"Live Casino",thumbnail:"/games/live-blackjack.jpg",isLive:!0,minBet:5,maxBet:5e3,rtp:99.28,popularity:88,isNew:!1,isFeatured:!0},{id:"3",name:"Starburst",provider:"NetEnt",category:"Slots",thumbnail:"/games/starburst.jpg",isLive:!1,minBet:.1,maxBet:100,rtp:96.09,popularity:92,isNew:!1,isFeatured:!1},{id:"4",name:"Lightning Roulette",provider:"Evolution Gaming",category:"Live Casino",thumbnail:"/games/lightning-roulette.jpg",isLive:!0,minBet:.2,maxBet:2e4,rtp:97.3,popularity:90,isNew:!0,isFeatured:!0}],b={totalUsers:125847,totalBets:2847593,totalVolume:45892347.5,liveEvents:156},j=[{event:"Manchester United vs Liverpool",selection:"Over 2.5 Goals",odds:1.85,percentage:78},{event:"Lakers vs Warriors",selection:"Lakers to Win",odds:1.95,percentage:65},{event:"Chelsea vs Arsenal",selection:"Arsenal to Win",odds:1.65,percentage:82}],y=[{username:"BetMaster2023",amount:15750,game:"Football Combo",time:"2 minutes ago"},{username:"LuckyPlayer",amount:8500,game:"Live Blackjack",time:"5 minutes ago"},{username:"SportsFan",amount:3200,game:"Basketball",time:"8 minutes ago"}];function v(){let[e,s]=(0,i.useState)("all"),[a,r]=(0,i.useState)(!1),{addSelection:n}=(0,m.I)();(0,i.useEffect)(()=>{r(!0)},[]);let d=u.filter(s=>"live"===e?s.isLive:"upcoming"!==e||!s.isLive),c=(e,s,a)=>{let t={outcomeId:a.id,eventId:e.id,marketId:s.id,eventName:"".concat(e.homeTeam.name," vs ").concat(e.awayTeam.name),marketName:s.name,outcomeName:a.name,odds:a.odds,isLive:e.isLive};n(t)},o=e=>{if(!a)return"--:--";let s=new Date(e);return s.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1})};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex space-x-1 bg-dark-800 p-1 rounded-lg max-w-fit",children:[{id:"all",label:"All Events"},{id:"live",label:"Live"},{id:"upcoming",label:"Upcoming"}].map(a=>(0,t.jsx)("button",{onClick:()=>s(a.id),className:"py-2 px-4 rounded-md font-medium transition-all duration-200 whitespace-nowrap ".concat(e===a.id?"bg-primary-600 text-white":"text-gray-400 hover:text-white hover:bg-dark-700"),children:a.label},a.id))}),(0,t.jsx)("div",{className:"space-y-4",children:d.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1*s},className:"bg-dark-800 rounded-xl border border-dark-700 hover:border-dark-600 transition-colors overflow-hidden",children:[(0,t.jsx)("div",{className:"p-4 border-b border-dark-700",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[e.isLive&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-red-400 text-sm font-medium",children:"LIVE"})]}),!e.isLive&&(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-gray-400",children:[(0,t.jsx)(p.Z,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:o(e.startTime)})]})]}),e.isLive&&e.minute&&(0,t.jsxs)("div",{className:"text-primary-400 font-medium",children:[e.minute,"' ",e.period]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xs font-bold text-white",children:e.homeTeam.name.charAt(0)})}),(0,t.jsx)("span",{className:"text-white font-medium",children:e.homeTeam.name}),e.isLive&&void 0!==e.homeScore&&(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:e.homeScore})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-dark-600 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-xs font-bold text-white",children:e.awayTeam.name.charAt(0)})}),(0,t.jsx)("span",{className:"text-white font-medium",children:e.awayTeam.name}),e.isLive&&void 0!==e.awayScore&&(0,t.jsx)("span",{className:"text-2xl font-bold text-white",children:e.awayScore})]})]})}),(0,t.jsx)("div",{className:"space-y-4",children:e.markets.map(s=>(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-gray-400 text-sm font-medium mb-2",children:s.name}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:s.outcomes.map(a=>(0,t.jsxs)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>c(e,s,a),className:"p-3 rounded-lg border transition-all duration-200 ".concat("up"===a.trend?"bg-green-500/10 border-green-500/30 hover:bg-green-500/20":"down"===a.trend?"bg-red-500/10 border-red-500/30 hover:bg-red-500/20":"bg-dark-700 border-dark-600 hover:bg-dark-600"),children:[(0,t.jsx)("div",{className:"text-white font-medium text-sm mb-1",children:a.name}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-primary-400 font-bold",children:a.odds.toFixed(2)}),a.trend&&"neutral"!==a.trend&&(0,t.jsx)("span",{className:"text-xs ".concat("up"===a.trend?"text-green-400":"text-red-400"),children:"up"===a.trend?"↗":"↘"})]})]},a.id))})]},s.id))})]})]},e.id))}),0===d.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-gray-400 text-lg mb-2",children:"No events found"}),(0,t.jsx)("div",{className:"text-gray-500 text-sm",children:"Try adjusting your filter or check back later"})]})]})}var f=a(6022);function N(){let[e,s]=(0,i.useState)(!1),{addSelection:a}=(0,m.I)();(0,i.useEffect)(()=>{s(!0)},[]);let r=u.filter(e=>e.isLive),n=(e,s,t)=>{let i={outcomeId:t.id,eventId:e.id,marketId:s.id,eventName:"".concat(e.homeTeam.name," vs ").concat(e.awayTeam.name),marketName:s.name,outcomeName:t.name,odds:t.odds,isLive:e.isLive};a(i)};return(0,t.jsxs)("div",{className:"betting-section-spacing",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)(f.Z,{className:"h-6 w-6 text-red-400"})]}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Live Events"}),(0,t.jsxs)("span",{className:"bg-red-500 text-white text-sm px-3 py-1 rounded-full",children:[r.length," Live"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:r.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.4,delay:.1*s},className:"bg-gradient-to-br from-dark-800 to-dark-900 rounded-xl border border-red-500/30 overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-red-500/10 border-b border-red-500/30 p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-red-400 font-semibold text-sm",children:"LIVE"})]}),(0,t.jsxs)("div",{className:"text-white font-bold",children:[e.minute,"' ",e.period]})]})}),(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,t.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-white",children:e.homeTeam.name.charAt(0)})}),(0,t.jsx)("span",{className:"text-white font-medium",children:e.homeTeam.name})]}),(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:e.homeScore})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 bg-dark-600 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-white",children:e.awayTeam.name.charAt(0)})}),(0,t.jsx)("span",{className:"text-white font-medium",children:e.awayTeam.name})]}),(0,t.jsx)("span",{className:"text-3xl font-bold text-white",children:e.awayScore})]})]})}),(0,t.jsx)("div",{className:"space-y-4",children:e.markets.map(s=>(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-gray-400 text-sm font-medium mb-3",children:s.name}),(0,t.jsx)("div",{className:"grid grid-cols-3 gap-2",children:s.outcomes.map(a=>(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>n(e,s,a),className:"bg-dark-700 hover:bg-dark-600 border border-dark-600 hover:border-primary-500 rounded-lg p-3 transition-all duration-200 group",children:[(0,t.jsx)("div",{className:"text-white font-medium text-sm mb-1 group-hover:text-primary-400",children:a.name}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-primary-400 font-bold text-lg",children:a.odds.toFixed(2)}),a.trend&&"neutral"!==a.trend&&(0,t.jsx)(l.E.span,{initial:{scale:0},animate:{scale:1},className:"text-sm ".concat("up"===a.trend?"text-green-400":"text-red-400"),children:"up"===a.trend?"↗":"↘"})]})]},a.id))})]},s.id))})]})]},e.id))}),0===r.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(p.Z,{className:"h-16 w-16 text-gray-600 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Live Events"}),(0,t.jsx)("p",{className:"text-gray-400",children:"Check back soon for live betting opportunities"})]})]})}var w=a(7277),k=a(9300),E=a(3274),L=a(4697),T=a(8748);function S(){let{isOpen:e,selections:s,stake:a,betType:r,totalOdds:n,potentialWin:d,closeSlip:c,removeSelection:o,clearSelections:x,setStake:h,setBetType:p,canPlaceBet:u}=(0,m.I)(),[g,b]=(0,i.useState)(!1),j=async()=>{if(u){b(!0);try{await new Promise(e=>setTimeout(e,1500)),T.default.success("Bet placed successfully! Potential win: $".concat(d.toFixed(2))),x(),c()}catch(e){T.default.error("Failed to place bet. Please try again.")}finally{b(!1)}}},y=e=>{let s=parseFloat(e)||0;h(s)};return(0,t.jsx)(w.M,{children:e&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:c,className:"fixed inset-0 bg-black/50 z-40 lg:hidden"}),(0,t.jsxs)(l.E.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",damping:25,stiffness:200},className:"fixed right-0 top-0 h-full w-full max-w-md bg-dark-900 border-l border-dark-700 z-50 flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-dark-700",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-white",children:"Betting Slip"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[s.length>0&&(0,t.jsx)("button",{onClick:x,className:"p-2 rounded-lg hover:bg-dark-700 transition-colors",title:"Clear all selections",children:(0,t.jsx)(k.Z,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("button",{onClick:c,className:"p-2 rounded-lg hover:bg-dark-700 transition-colors",children:(0,t.jsx)(E.Z,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===s.length?(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-8 text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-dark-700 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(L.Z,{className:"h-8 w-8 text-gray-400"})}),(0,t.jsx)("h3",{className:"text-white font-medium mb-2",children:"Your bet slip is empty"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Click on odds to add selections to your bet slip"})]}):(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[s.length>1&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Bet Type"}),(0,t.jsx)("div",{className:"flex space-x-1 bg-dark-800 p-1 rounded-lg",children:[{id:"single",label:"Single"},{id:"combo",label:"Combo"},{id:"system",label:"System"}].map(e=>(0,t.jsx)("button",{onClick:()=>p(e.id),className:"flex-1 py-2 px-3 rounded-md text-sm font-medium transition-colors ".concat(r===e.id?"bg-primary-600 text-white":"text-gray-400 hover:text-white"),children:e.label},e.id))})]}),(0,t.jsx)("div",{className:"space-y-3",children:s.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"bg-dark-800 rounded-lg p-3 border border-dark-600",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"text-white font-medium text-sm",children:e.eventName}),(0,t.jsx)("p",{className:"text-gray-400 text-xs",children:e.marketName})]}),(0,t.jsx)("button",{onClick:()=>o(e.outcomeId),className:"p-1 rounded hover:bg-dark-700 transition-colors",children:(0,t.jsx)(E.Z,{className:"h-4 w-4 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-white font-medium",children:e.outcomeName}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isLive&&(0,t.jsx)("span",{className:"text-red-400 text-xs",children:"LIVE"}),(0,t.jsx)("span",{className:"text-primary-400 font-bold",children:e.odds.toFixed(2)})]})]})]},e.outcomeId))}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-300",children:"Stake Amount"}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-2",children:[10,25,50,100].map(e=>(0,t.jsxs)("button",{onClick:()=>h(e),className:"py-2 px-3 rounded-lg text-sm font-medium transition-colors ".concat(a===e?"bg-primary-600 text-white":"bg-dark-700 text-gray-300 hover:bg-dark-600"),children:["$",e]},e))}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",children:"$"}),(0,t.jsx)("input",{type:"number",value:a||"",onChange:e=>y(e.target.value),placeholder:"Enter stake amount",className:"w-full pl-8 pr-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),(0,t.jsxs)("div",{className:"bg-dark-700 rounded-lg p-4 space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Total Odds:"}),(0,t.jsx)("span",{className:"text-white font-medium",children:n.toFixed(2)})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Stake:"}),(0,t.jsxs)("span",{className:"text-white font-medium",children:["$",a.toFixed(2)]})]}),(0,t.jsx)("div",{className:"border-t border-dark-600 pt-2",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-300 font-medium",children:"Potential Win:"}),(0,t.jsxs)("span",{className:"text-green-400 font-bold text-lg",children:["$",d.toFixed(2)]})]})})]})]})}),s.length>0&&(0,t.jsx)("div",{className:"p-4 border-t border-dark-700",children:(0,t.jsx)(l.E.button,{whileHover:{scale:u?1.02:1},whileTap:{scale:u?.98:1},onClick:j,disabled:!u||g,className:"w-full py-3 rounded-lg font-semibold transition-all duration-200 ".concat(u&&!g?"bg-primary-600 hover:bg-primary-700 text-white":"bg-dark-600 text-gray-400 cursor-not-allowed"),children:g?(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Placing Bet..."})]}):"Place Bet - $".concat(d.toFixed(2))})})]})]})})}var C=a(831),Z=a(3511),I=a(5907);function A(){let[e,s]=(0,i.useState)("all"),a=[{id:"all",label:"All Games",count:g.length},{id:"slots",label:"Slots",count:g.filter(e=>"Slots"===e.category).length},{id:"live",label:"Live Casino",count:g.filter(e=>e.isLive).length},{id:"featured",label:"Featured",count:g.filter(e=>e.isFeatured).length}],r=g.filter(s=>"all"===e||("slots"===e?"Slots"===s.category:"live"===e?s.isLive:"featured"!==e||s.isFeatured));return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(C.Z,{className:"h-6 w-6 text-purple-400"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Casino Games"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:a.map(a=>(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>s(a.id),className:"flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ".concat(e===a.id?"bg-purple-600 text-white":"bg-dark-700 text-gray-300 hover:bg-dark-600 hover:text-white"),children:[(0,t.jsx)("span",{children:a.label}),(0,t.jsx)("span",{className:"text-xs bg-black/20 px-2 py-1 rounded-full",children:a.count})]},a.id))}),(0,t.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-4 lg:gap-6 auto-rows-fr",children:r.map((e,s)=>(0,t.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.05*s},whileHover:{scale:1.05},className:"group cursor-pointer",children:(0,t.jsxs)("div",{className:"relative bg-dark-800 rounded-xl overflow-hidden border border-dark-700 hover:border-purple-500/50 transition-all duration-300",children:[(0,t.jsxs)("div",{className:"aspect-square bg-gradient-to-br from-purple-500/20 to-pink-500/20 flex items-center justify-center relative overflow-hidden",children:[(0,t.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center",children:(0,t.jsx)(C.Z,{className:"h-12 w-12 text-white/80"})}),(0,t.jsxs)("div",{className:"absolute top-2 left-2 flex flex-col space-y-1",children:[e.isNew&&(0,t.jsx)("span",{className:"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium",children:"NEW"}),e.isFeatured&&(0,t.jsx)("span",{className:"bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-medium",children:"⭐ FEATURED"}),e.isLive&&(0,t.jsxs)("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center space-x-1",children:[(0,t.jsx)("div",{className:"w-1 h-1 bg-white rounded-full animate-pulse"}),(0,t.jsx)("span",{children:"LIVE"})]})]}),(0,t.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center",children:(0,t.jsx)(l.E.div,{initial:{scale:0},whileHover:{scale:1},className:"w-16 h-16 bg-white rounded-full flex items-center justify-center",children:(0,t.jsx)(Z.Z,{className:"h-8 w-8 text-purple-600 ml-1"})})}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsxs)("span",{className:"bg-black/70 text-white text-xs px-2 py-1 rounded-full",children:["RTP ",e.rtp,"%"]})})]}),(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsx)("h3",{className:"text-white font-medium text-sm mb-1 group-hover:text-purple-400 transition-colors",children:e.name}),(0,t.jsx)("p",{className:"text-gray-400 text-xs mb-2",children:e.provider}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,t.jsxs)("span",{className:"text-gray-500",children:["$",e.minBet," - $",e.maxBet]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(I.Z,{className:"h-3 w-3 text-yellow-400"}),(0,t.jsx)("span",{className:"text-gray-400",children:e.popularity})]})]})]})]})},e.id))}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200",children:"Load More Games"})}),(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 rounded-xl p-6 text-white relative overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,t.jsx)("div",{className:"relative z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-2xl font-bold mb-2",children:"\uD83C\uDFB0 Progressive Jackpot"}),(0,t.jsx)("p",{className:"text-lg opacity-90",children:"Current jackpot amount"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-4xl font-bold",children:"$2,847,593"}),(0,t.jsx)("div",{className:"text-sm opacity-75",children:"and growing..."})]})]})}),(0,t.jsx)("div",{className:"absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full animate-pulse"}),(0,t.jsx)("div",{className:"absolute bottom-4 left-20 w-8 h-8 bg-yellow-400/20 rounded-full animate-bounce"})]})]})}var B=a(8763),F=a(8203),M=a(4929);function D(e){let{end:s,duration:a=2,prefix:r="",suffix:n="",decimals:d=0,className:c="",delay:o=0,formatNumber:m=!0}=e,[x,h]=(0,i.useState)(0),[p,u]=(0,i.useState)(!1),g=(0,i.useRef)(null),b=(0,M.Y)(g,{once:!0,margin:"-100px"});return(0,i.useEffect)(()=>{if(b&&!p){u(!0);let e=setTimeout(()=>{let e,t;let i=l=>{e||(e=l);let r=Math.min((l-e)/(1e3*a),1),n=Math.floor((1-Math.pow(1-r,4))*s);h(n),r<1?t=requestAnimationFrame(i):h(s)};return t=requestAnimationFrame(i),()=>{t&&cancelAnimationFrame(t)}},1e3*o);return()=>clearTimeout(e)}},[b,p,s,a,o]),(0,t.jsxs)(l.E.span,{ref:g,initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.5,delay:o},className:c,children:[r,d>0?x.toFixed(d):m&&x>=1e3?x.toLocaleString():x.toString(),n]})}function $(e){let{end:s,duration:a=2,delay:i=0,className:l="",currency:r="$",showDecimals:n=!0}=e;return(0,t.jsx)(D,{end:s,duration:a,delay:i,className:l,prefix:r,decimals:n?1:0,formatNumber:!0})}function H(e){let{end:s,duration:a=2,delay:i=0,className:l="",autoSuffix:r=!0}=e;return(0,t.jsx)(D,{end:r?s>=1e6?s/1e6:s>=1e3?s/1e3:s:s,duration:a,delay:i,className:l,suffix:r?s>=1e6?"M":s>=1e3?"K":"":"",decimals:s>=1e6?1:0,formatNumber:!1})}let W=[{id:"users",label:"Active Users",value:b.totalUsers,displayValue:b.totalUsers.toLocaleString(),change:"+12.5%",changeType:"positive",icon:B.Z,color:"from-blue-500 to-blue-600",type:"number"},{id:"volume",label:"Total Volume",value:b.totalVolume/1e6,displayValue:"$".concat((b.totalVolume/1e6).toFixed(1),"M"),change:"+8.2%",changeType:"positive",icon:L.Z,color:"from-green-500 to-green-600",type:"currency"},{id:"bets",label:"Total Bets",value:b.totalBets/1e6,displayValue:(b.totalBets/1e6).toFixed(1)+"M",change:"+15.3%",changeType:"positive",icon:F.Z,color:"from-purple-500 to-purple-600",type:"large"},{id:"live",label:"Live Events",value:b.liveEvents,displayValue:b.liveEvents.toString(),change:"+5",changeType:"positive",icon:f.Z,color:"from-red-500 to-red-600",type:"number"}];function V(){return(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-4 gap-4 lg:gap-6 xl:gap-8 mb-8",children:[W.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1*s},className:"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700 hover:border-dark-600 transition-all duration-300 h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{className:"p-2.5 lg:p-3 rounded-lg bg-gradient-to-r ".concat(e.color," flex-shrink-0"),children:(0,t.jsx)(e.icon,{className:"h-5 w-5 lg:h-6 lg:w-6 text-white"})}),(0,t.jsx)("div",{className:"text-sm font-medium ".concat("positive"===e.changeType?"text-green-400":"text-red-400"),children:e.change})]}),(0,t.jsxs)("div",{className:"flex-1 flex flex-col justify-end",children:[(0,t.jsxs)("h3",{className:"text-xl lg:text-2xl font-bold text-white mb-1 leading-tight",children:["currency"===e.type?(0,t.jsx)($,{end:e.value,duration:2.5,delay:.2*s+.5,currency:"$",showDecimals:!0}):"large"===e.type?(0,t.jsx)(H,{end:e.value,duration:2.5,delay:.2*s+.5,autoSuffix:!0}):(0,t.jsx)(D,{end:e.value,duration:2.5,delay:.2*s+.5,formatNumber:!0}),"currency"===e.type&&"M"]}),(0,t.jsx)("p",{className:"text-gray-400 text-sm leading-tight",children:e.label})]})]},e.id)),(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"col-span-2 md:col-span-2 lg:col-span-4 xl:col-span-4 2xl:col-span-4 bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white flex items-center gap-2",children:[(0,t.jsx)(F.Z,{className:"h-5 w-5 text-yellow-400"}),"Recent Big Winners"]}),(0,t.jsx)("span",{className:"text-sm text-gray-400",children:"Live updates"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:y.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.5+.1*s},className:"bg-dark-700 rounded-lg p-4 border border-dark-600",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-white font-medium",children:e.username}),(0,t.jsxs)("span",{className:"text-green-400 font-bold",children:["$",(0,t.jsx)(D,{end:e.amount,duration:2,delay:.8+.15*s,formatNumber:!0})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-400",children:e.game}),(0,t.jsx)("span",{className:"text-gray-500",children:e.time})]})]},s))})]})]})}var P=a(8447);function U(){return(0,t.jsxs)("div",{className:"bg-dark-800 rounded-xl border border-dark-700 p-6 h-fit sticky top-24",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)(P.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Trending Bets"}),(0,t.jsx)(f.Z,{className:"h-4 w-4 text-orange-400"})]}),(0,t.jsx)("div",{className:"space-y-4",children:j.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.4,delay:.1*s},className:"bg-dark-700 rounded-lg p-4 border border-dark-600 hover:border-primary-500/50 transition-colors cursor-pointer group",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"text-white font-medium text-sm group-hover:text-primary-400 transition-colors",children:e.event}),(0,t.jsx)("p",{className:"text-gray-400 text-xs mt-1",children:e.selection})]}),(0,t.jsx)("span",{className:"text-primary-400 font-bold",children:e.odds.toFixed(2)})]}),(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-full bg-dark-600 rounded-full h-2 flex-1 max-w-[100px]",children:(0,t.jsx)(l.E.div,{initial:{width:0},animate:{width:"".concat(e.percentage,"%")},transition:{duration:1,delay:.5+.1*s},className:"bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full"})}),(0,t.jsxs)("span",{className:"text-gray-400 text-xs",children:[e.percentage,"%"]})]})})]},s))}),(0,t.jsx)(l.E.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full mt-4 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium transition-colors",children:"View All Trends"})]})}var G=a(1690),_=a(4525),z=a(1859),O=a(9214),R=a(787);function q(){let[e,s]=(0,i.useState)(""),[a,r]=(0,i.useState)(""),[n,d]=(0,i.useState)(!1),[c,o]=(0,i.useState)(""),{login:m,isLoading:h}=(0,x.a)(),p=async s=>{if(s.preventDefault(),o(""),!e||!a){o("Please fill in all fields");return}let t=await m(e,a);t||o("Invalid credentials. Please try again.")};return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-white mb-2",children:"Login to Your Account"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Access your personalized betting dashboard"})]}),(0,t.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[c&&(0,t.jsx)(l.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"bg-red-500/10 border border-red-500/20 rounded-lg p-3",children:(0,t.jsx)("p",{className:"text-red-400 text-sm text-center",children:c})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(G.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,t.jsx)("input",{type:"email",value:e,onChange:e=>s(e.target.value),placeholder:"Email address",className:"w-full pl-10 pr-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all",disabled:h})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(_.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,t.jsx)("input",{type:n?"text":"password",value:a,onChange:e=>r(e.target.value),placeholder:"Password",className:"w-full pl-10 pr-12 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all",disabled:h}),(0,t.jsx)("button",{type:"button",onClick:()=>d(!n),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",disabled:h,children:n?(0,t.jsx)(z.Z,{className:"h-5 w-5"}):(0,t.jsx)(O.Z,{className:"h-5 w-5"})})]})]}),(0,t.jsx)(l.E.button,{type:"submit",disabled:h,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed",children:h?(0,t.jsx)("div",{className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{children:"Login"}),(0,t.jsx)(R.Z,{className:"h-4 w-4"})]})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("p",{className:"text-gray-400 text-sm",children:["Don't have an account?"," ",(0,t.jsx)("button",{type:"button",className:"text-primary-400 hover:text-primary-300 font-medium transition-colors",children:"Sign up"})]})}),(0,t.jsx)("div",{className:"text-center pt-2",children:(0,t.jsx)("p",{className:"text-gray-500 text-xs",children:"Demo: Use any email and password (6+ chars)"})})]})]})}let K=[{id:"1",match:"Manchester United vs Liverpool",bet:"Over 2.5 Goals",odds:1.85,stake:50,status:"live",timeLeft:"45 min"},{id:"2",match:"Lakers vs Warriors",bet:"Lakers to Win",odds:2.1,stake:25,status:"pending",timeLeft:"2 hours"}],Y=[{id:"1",match:"Chelsea vs Arsenal",bet:"Arsenal to Win",odds:1.65,stake:100,payout:165,status:"won",date:"2 hours ago"},{id:"2",match:"Real Madrid vs Barcelona",bet:"Over 3.5 Goals",odds:2.2,stake:75,payout:0,status:"lost",date:"1 day ago"}],J=[{name:"Football",icon:"⚽",events:156},{name:"Basketball",icon:"\uD83C\uDFC0",events:89},{name:"Tennis",icon:"\uD83C\uDFBE",events:234}];function Q(){let{user:e}=(0,x.a)();return e?(0,t.jsxs)("div",{className:"w-full space-y-8",children:[(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center lg:text-left",children:[(0,t.jsxs)("h1",{className:"text-3xl lg:text-4xl font-bold text-white mb-2",children:["Welcome back, ",(0,t.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500",children:e.username})]}),(0,t.jsx)("p",{className:"text-gray-400 text-lg",children:"Ready to place your next winning bet?"})]}),(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,t.jsxs)("div",{className:"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(L.Z,{className:"h-6 w-6 text-green-400"}),(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Balance"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-white",children:["$",(0,t.jsx)(D,{end:e.balance,duration:2,delay:.2,decimals:2})]})]}),(0,t.jsxs)("div",{className:"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(F.Z,{className:"h-6 w-6 text-yellow-400"}),(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Total Bets"})]}),(0,t.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,t.jsx)(D,{end:e.totalBets,duration:2,delay:.3})})]}),(0,t.jsxs)("div",{className:"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(P.Z,{className:"h-6 w-6 text-blue-400"}),(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Winnings"})]}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-white",children:["$",(0,t.jsx)(D,{end:e.totalWinnings,duration:2,delay:.4,decimals:2})]})]}),(0,t.jsxs)("div",{className:"bg-dark-800 rounded-xl p-4 lg:p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(f.Z,{className:"h-6 w-6 text-red-400"}),(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Active Bets"})]}),(0,t.jsx)("p",{className:"text-2xl font-bold text-white",children:(0,t.jsx)(D,{end:K.length,duration:2,delay:.5})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8",children:[(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.2},className:"bg-dark-800 rounded-xl p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)(p.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Active Bets"})]}),(0,t.jsx)("div",{className:"space-y-4",children:K.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3+.1*s},className:"bg-dark-700 rounded-lg p-4 border border-dark-600",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"text-white font-medium text-sm",children:e.match}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("live"===e.status?"bg-red-500/20 text-red-400":"bg-yellow-500/20 text-yellow-400"),children:"live"===e.status?"LIVE":"PENDING"})]}),(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:e.bet}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-300",children:["Stake: $",e.stake]}),(0,t.jsxs)("span",{className:"text-primary-400",children:["Odds: ",e.odds]}),(0,t.jsx)("span",{className:"text-gray-400",children:e.timeLeft})]})]},e.id))})]}),(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.6,delay:.3},className:"bg-dark-800 rounded-xl p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)(P.Z,{className:"h-5 w-5 text-secondary-400"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Recent History"})]}),(0,t.jsx)("div",{className:"space-y-4",children:Y.map((e,s)=>(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4+.1*s},className:"bg-dark-700 rounded-lg p-4 border border-dark-600",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"text-white font-medium text-sm",children:e.match}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("won"===e.status?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:e.status.toUpperCase()})]}),(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:e.bet}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-300",children:["Stake: $",e.stake]}),(0,t.jsx)("span",{className:"won"===e.status?"text-green-400":"text-red-400",children:"won"===e.status?"+$".concat(e.payout-e.stake):"-$".concat(e.stake)}),(0,t.jsx)("span",{className:"text-gray-400",children:e.date})]})]},e.id))})]})]}),(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"bg-dark-800 rounded-xl p-6 border border-dark-700",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,t.jsx)(I.Z,{className:"h-5 w-5 text-yellow-400"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Quick Access - Favorite Sports"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:J.map((e,s)=>(0,t.jsx)(l.E.button,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.5+.1*s},whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-dark-700 hover:bg-dark-600 rounded-lg p-4 border border-dark-600 hover:border-primary-500/50 transition-all duration-300",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,t.jsx)("h4",{className:"text-white font-medium mb-1",children:e.name}),(0,t.jsxs)("p",{className:"text-gray-400 text-sm",children:[e.events," events"]})]})},e.name))})]})]}):null}function X(){let{isAuthenticated:e,isLoading:s}=(0,x.a)();return s?(0,t.jsx)("div",{className:"flex items-center justify-center py-20",children:(0,t.jsx)(l.E.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-8 h-8 border-2 border-transparent border-t-primary-500 rounded-full"})}):(0,t.jsxs)(l.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:"relative min-h-[400px] flex items-center justify-center py-8 lg:py-12",children:[(0,t.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-secondary-500/5"}),(0,t.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500/10 rounded-full blur-3xl"}),(0,t.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary-500/10 rounded-full blur-3xl"})]}),(0,t.jsx)("div",{className:"relative z-10 w-full",children:e?(0,t.jsx)(Q,{}):(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12",children:[(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"flex-1 text-center lg:text-left",children:[(0,t.jsxs)(l.E.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.2},className:"text-3xl lg:text-5xl xl:text-6xl font-bold mb-4 leading-tight",children:[(0,t.jsx)("span",{className:"text-white",children:"Welcome to"}),(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500",children:"Kesar Mango"})]}),(0,t.jsx)(l.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-lg lg:text-xl text-gray-300 mb-6 max-w-xl",children:"Explore live sports betting, casino games, and more. Login to unlock personalized features and track your bets."}),(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"flex flex-col sm:flex-row gap-3 justify-center lg:justify-start",children:[(0,t.jsx)("div",{className:"bg-dark-800/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-dark-700",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-gray-300 text-sm",children:"Live Events"})]})}),(0,t.jsx)("div",{className:"bg-dark-800/50 backdrop-blur-sm rounded-lg px-4 py-2 border border-dark-700",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-gray-300 text-sm",children:"Instant Payouts"})]})})]})]}),(0,t.jsx)(l.E.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.3},className:"flex-shrink-0 w-full max-w-sm",children:(0,t.jsx)("div",{className:"bg-dark-800/80 backdrop-blur-md rounded-xl p-6 border border-dark-700 shadow-2xl",children:(0,t.jsx)(q,{})})})]})})]})}var ee=a(2766);function es(){let{isAuthenticated:e}=(0,x.a)();return e?null:(0,t.jsx)(l.E.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},className:"bg-gradient-to-r from-primary-600/10 to-secondary-600/10 border border-primary-500/20 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(ee.Z,{className:"h-5 w-5 text-primary-400 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-white font-medium text-sm",children:"Login to unlock personalized features"}),(0,t.jsx)("p",{className:"text-gray-400 text-xs",children:"Track your bets, view history, and access exclusive promotions"})]})]}),(0,t.jsxs)(l.E.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{let e=document.querySelector("main");null==e||e.scrollIntoView({behavior:"smooth"})},className:"flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:[(0,t.jsx)("span",{children:"Login"}),(0,t.jsx)(R.Z,{className:"h-4 w-4"})]})]})})}function ea(e){let{children:s,className:a=""}=e,{isAuthenticated:i}=(0,x.a)();return(0,t.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"relative ".concat(a),children:[!i&&(0,t.jsx)("div",{className:"absolute inset-0 bg-dark-950/20 backdrop-blur-[0.5px] rounded-lg z-10 pointer-events-none"}),(0,t.jsx)("div",{className:i?"opacity-100":"opacity-90",children:s})]})}function et(){let[e,s]=(0,i.useState)("sports");return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950",children:[(0,t.jsx)(h,{}),(0,t.jsx)("main",{className:"w-full min-w-0",children:(0,t.jsxs)("div",{className:"w-full px-4 lg:px-6 xl:px-8 2xl:px-12 py-6 lg:py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(X,{})}),(0,t.jsx)(V,{}),(0,t.jsx)(es,{}),(0,t.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"mb-8",children:(0,t.jsx)("div",{className:"flex space-x-1 bg-dark-800 p-1 rounded-xl w-full max-w-md mx-auto lg:mx-0 lg:max-w-fit",children:[{id:"sports",label:"Sports Betting",icon:"⚽"},{id:"live",label:"Live Events",icon:"\uD83D\uDD34"},{id:"casino",label:"Casino Games",icon:"\uD83C\uDFB0"}].map(a=>(0,t.jsxs)("button",{onClick:()=>s(a.id),className:"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-lg font-medium transition-all duration-200 ".concat(e===a.id?"bg-primary-600 text-white shadow-lg":"text-gray-400 hover:text-white hover:bg-dark-700"),children:[(0,t.jsx)("span",{className:"text-lg",children:a.icon}),(0,t.jsx)("span",{className:"hidden sm:inline",children:a.label})]},a.id))})}),(0,t.jsx)(ea,{className:"w-full",children:(0,t.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.4},className:"w-full",children:["sports"===e&&(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 lg:gap-6 items-start",children:[(0,t.jsx)("div",{className:"lg:col-span-2 xl:col-span-3 2xl:col-span-4",children:(0,t.jsx)(v,{})}),(0,t.jsx)("div",{className:"lg:col-span-1 xl:col-span-1 2xl:col-span-1",children:(0,t.jsx)(U,{})})]}),"live"===e&&(0,t.jsx)(N,{}),"casino"===e&&(0,t.jsx)(A,{})]},e)})]})}),(0,t.jsx)(S,{})]})}}},function(e){e.O(0,[216,592,744],function(){return e(e.s=2421)}),_N_E=e.O()}]);