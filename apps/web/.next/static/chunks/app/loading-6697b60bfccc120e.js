(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[555],{3287:function(e,a,t){Promise.resolve().then(t.bind(t,3738))},3738:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return n}});var s=t(7573),i=t(5021),r=t(7653);function n(){let[e,a]=(0,r.useState)(""),[t,n]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let e=setInterval(()=>{a(e=>e.length>=3?"":e+".")},500);return()=>clearInterval(e)},[]),(0,r.useEffect)(()=>{let e=setInterval(()=>{n(e=>e>=100?100:Math.min(e+(e<30?8:e<70?4:e<90?2:1),100))},150);return()=>clearInterval(e)},[]),(0,s.jsxs)("div",{className:"fixed inset-0 z-50 bg-gradient-to-br from-dark-950 via-dark-900 to-dark-950 flex items-center justify-center",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,215,0,0.1),transparent_50%)]"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(255,165,0,0.1),transparent_50%)]"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(255,140,0,0.1),transparent_50%)]"})]}),(0,s.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-1/3 left-1/3 w-96 h-96 bg-primary-500/5 rounded-full blur-3xl"}),(0,s.jsx)("div",{className:"absolute bottom-1/3 right-1/3 w-96 h-96 bg-secondary-500/5 rounded-full blur-3xl"})]}),(0,s.jsxs)("div",{className:"relative z-10 flex flex-col items-center space-y-8",children:[(0,s.jsxs)(i.E.div,{initial:{scale:0,rotate:-180},animate:{scale:1,rotate:0},transition:{duration:.8,ease:"easeOut"},className:"relative",children:[(0,s.jsx)(i.E.div,{animate:{rotate:360},transition:{duration:3,repeat:1/0,ease:"linear"},className:"w-24 h-24 border-4 border-transparent border-t-primary-500 border-r-secondary-500 rounded-full"}),(0,s.jsx)(i.E.div,{animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"absolute inset-2 w-16 h-16 border-2 border-transparent border-b-primary-400 border-l-secondary-400 rounded-full"}),(0,s.jsx)(i.E.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.3,duration:.5},className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center shadow-lg",children:(0,s.jsx)("span",{className:"text-white font-bold text-xl",children:"K"})})})]}),(0,s.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5,duration:.6},className:"text-center space-y-2",children:[(0,s.jsxs)("h1",{className:"text-2xl font-bold text-white",children:["Kesar ",(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-secondary-500",children:"Mango"})]}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Premium Sports & Casino Betting"})]}),(0,s.jsx)(i.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.4},className:"text-center",children:(0,s.jsxs)("p",{className:"text-gray-500 text-sm font-medium",children:["Loading your betting experience",e]})}),(0,s.jsx)(i.E.div,{initial:{opacity:0,width:0},animate:{opacity:1,width:"100%"},transition:{delay:1,duration:.8},className:"w-64 h-2 bg-dark-700 rounded-full overflow-hidden",children:(0,s.jsx)(i.E.div,{initial:{width:"0%"},animate:{width:"".concat(t,"%")},transition:{duration:.3,ease:"easeOut"},className:"h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full"})}),(0,s.jsx)(i.E.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.2,duration:.4},className:"text-center",children:(0,s.jsxs)("p",{className:"text-gray-400 text-xs font-medium",children:[t,"% Complete"]})})]}),(0,s.jsxs)(i.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.6},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center",children:[(0,s.jsx)("p",{className:"text-gray-600 text-xs",children:"Powered by cutting-edge technology"}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-1 mt-1",children:[(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-500 rounded-full animate-pulse"}),(0,s.jsx)("div",{className:"w-1 h-1 bg-secondary-500 rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),(0,s.jsx)("div",{className:"w-1 h-1 bg-primary-500 rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]})]})]})}}},function(e){e.O(0,[216,744],function(){return e(e.s=3287)}),_N_E=e.O()}]);