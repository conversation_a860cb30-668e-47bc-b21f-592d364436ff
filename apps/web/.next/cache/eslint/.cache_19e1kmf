[{"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx": "1", "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx": "2", "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx": "3", "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx": "4", "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx": "5", "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx": "6", "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx": "7", "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx": "8", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx": "9", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx": "10", "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx": "11", "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx": "12", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx": "13", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx": "14", "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx": "15", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx": "16", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx": "17", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx": "18", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx": "19", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx": "20", "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts": "21", "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts": "22", "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts": "23", "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts": "24", "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts": "25", "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts": "26", "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts": "27", "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx": "28", "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx": "29", "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts": "30"}, {"size": 3137, "mtime": 1751699209137, "results": "31", "hashOfConfig": "32"}, {"size": 6040, "mtime": 1751705019819, "results": "33", "hashOfConfig": "32"}, {"size": 4597, "mtime": 1751707480403, "results": "34", "hashOfConfig": "32"}, {"size": 517, "mtime": 1751705033117, "results": "35", "hashOfConfig": "32"}, {"size": 397, "mtime": 1751699438674, "results": "36", "hashOfConfig": "32"}, {"size": 6509, "mtime": 1751704698962, "results": "37", "hashOfConfig": "32"}, {"size": 11299, "mtime": 1751689944143, "results": "38", "hashOfConfig": "32"}, {"size": 7623, "mtime": 1751691901763, "results": "39", "hashOfConfig": "32"}, {"size": 5747, "mtime": 1751698356220, "results": "40", "hashOfConfig": "32"}, {"size": 9576, "mtime": 1751704739543, "results": "41", "hashOfConfig": "32"}, {"size": 5542, "mtime": 1751707227033, "results": "42", "hashOfConfig": "32"}, {"size": 4369, "mtime": 1751705706949, "results": "43", "hashOfConfig": "32"}, {"size": 7696, "mtime": 1751707377653, "results": "44", "hashOfConfig": "32"}, {"size": 6315, "mtime": 1751691063359, "results": "45", "hashOfConfig": "32"}, {"size": 8933, "mtime": 1751707227049, "results": "46", "hashOfConfig": "32"}, {"size": 6887, "mtime": 1751707227043, "results": "47", "hashOfConfig": "32"}, {"size": 7923, "mtime": 1751707227043, "results": "48", "hashOfConfig": "32"}, {"size": 2631, "mtime": 1751691855369, "results": "49", "hashOfConfig": "32"}, {"size": 4197, "mtime": 1751698219580, "results": "50", "hashOfConfig": "32"}, {"size": 2288, "mtime": 1751696457432, "results": "51", "hashOfConfig": "32"}, {"size": 6833, "mtime": 1751690546091, "results": "52", "hashOfConfig": "32"}, {"size": 885, "mtime": 1751696440509, "results": "53", "hashOfConfig": "32"}, {"size": 6814, "mtime": 1751692521643, "results": "54", "hashOfConfig": "32"}, {"size": 4509, "mtime": 1751692491176, "results": "55", "hashOfConfig": "32"}, {"size": 7148, "mtime": 1751707227043, "results": "56", "hashOfConfig": "32"}, {"size": 1902, "mtime": 1751690107598, "results": "57", "hashOfConfig": "32"}, {"size": 7515, "mtime": 1751707227012, "results": "58", "hashOfConfig": "32"}, {"size": 2846, "mtime": 1751707331977, "results": "59", "hashOfConfig": "32"}, {"size": 6078, "mtime": 1751689552334, "results": "60", "hashOfConfig": "32"}, {"size": 4492, "mtime": 1751689524662, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wrrpjt", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx", ["152"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx", ["153"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx", ["154"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx", ["155"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts", ["156"], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts", [], [], {"ruleId": "157", "severity": 2, "message": "158", "line": 109, "column": 17, "nodeType": "159", "messageId": "160", "suggestions": "161"}, {"ruleId": "162", "severity": 1, "message": "163", "line": 126, "column": 23, "nodeType": "164", "endLine": 130, "endColumn": 25}, {"ruleId": "157", "severity": 2, "message": "158", "line": 66, "column": 33, "nodeType": "159", "messageId": "160", "suggestions": "165"}, {"ruleId": "157", "severity": 2, "message": "158", "line": 102, "column": 35, "nodeType": "159", "messageId": "160", "suggestions": "166"}, {"ruleId": "167", "severity": 1, "message": "168", "line": 271, "column": 1, "nodeType": "169", "endLine": 283, "endColumn": 2}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["170", "171", "172", "173"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["174", "175", "176", "177"], ["178", "179", "180", "181"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"messageId": "182", "data": "183", "fix": "184", "desc": "185"}, {"messageId": "182", "data": "186", "fix": "187", "desc": "188"}, {"messageId": "182", "data": "189", "fix": "190", "desc": "191"}, {"messageId": "182", "data": "192", "fix": "193", "desc": "194"}, {"messageId": "182", "data": "195", "fix": "196", "desc": "185"}, {"messageId": "182", "data": "197", "fix": "198", "desc": "188"}, {"messageId": "182", "data": "199", "fix": "200", "desc": "191"}, {"messageId": "182", "data": "201", "fix": "202", "desc": "194"}, {"messageId": "182", "data": "203", "fix": "204", "desc": "185"}, {"messageId": "182", "data": "205", "fix": "206", "desc": "188"}, {"messageId": "182", "data": "207", "fix": "208", "desc": "191"}, {"messageId": "182", "data": "209", "fix": "210", "desc": "194"}, "replaceWithAlt", {"alt": "211"}, {"range": "212", "text": "213"}, "Replace with `&apos;`.", {"alt": "214"}, {"range": "215", "text": "216"}, "Replace with `&lsquo;`.", {"alt": "217"}, {"range": "218", "text": "219"}, "Replace with `&#39;`.", {"alt": "220"}, {"range": "221", "text": "222"}, "Replace with `&rsquo;`.", {"alt": "211"}, {"range": "223", "text": "224"}, {"alt": "214"}, {"range": "225", "text": "226"}, {"alt": "217"}, {"range": "227", "text": "228"}, {"alt": "220"}, {"range": "229", "text": "230"}, {"alt": "211"}, {"range": "231", "text": "224"}, {"alt": "214"}, {"range": "232", "text": "226"}, {"alt": "217"}, {"range": "233", "text": "228"}, {"alt": "220"}, {"range": "234", "text": "230"}, "&apos;", [3007, 3121], "\n              We&apos;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&lsquo;", [3007, 3121], "\n              We&lsquo;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&#39;", [3007, 3121], "\n              We&#39;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&rsquo;", [3007, 3121], "\n              We&rsquo;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", [2495, 2497], "&apos; ", [2495, 2497], "&lsquo; ", [2495, 2497], "&#39; ", [2495, 2497], "&rsquo; ", [3665, 3667], [3665, 3667], [3665, 3667], [3665, 3667]]