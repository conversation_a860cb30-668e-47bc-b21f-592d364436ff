[{"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx": "1", "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx": "2", "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx": "3", "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx": "4", "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx": "5", "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx": "6", "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx": "7", "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx": "8", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx": "9", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx": "10", "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx": "11", "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx": "12", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx": "13", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx": "14", "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx": "15", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx": "16", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx": "17", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx": "18", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx": "19", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx": "20", "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts": "21", "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts": "22", "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts": "23", "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts": "24", "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts": "25", "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts": "26", "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts": "27", "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx": "28", "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx": "29", "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts": "30"}, {"size": 3137, "mtime": 1751699209137, "results": "31", "hashOfConfig": "32"}, {"size": 6040, "mtime": 1751705019819, "results": "33", "hashOfConfig": "32"}, {"size": 3581, "mtime": 1751701487475, "results": "34", "hashOfConfig": "32"}, {"size": 517, "mtime": 1751705033117, "results": "35", "hashOfConfig": "32"}, {"size": 397, "mtime": 1751699438674, "results": "36", "hashOfConfig": "32"}, {"size": 6509, "mtime": 1751704698962, "results": "37", "hashOfConfig": "32"}, {"size": 11299, "mtime": 1751689944143, "results": "38", "hashOfConfig": "32"}, {"size": 7623, "mtime": 1751691901763, "results": "39", "hashOfConfig": "32"}, {"size": 5747, "mtime": 1751698356220, "results": "40", "hashOfConfig": "32"}, {"size": 9576, "mtime": 1751704739543, "results": "41", "hashOfConfig": "32"}, {"size": 5547, "mtime": 1751706468253, "results": "42", "hashOfConfig": "32"}, {"size": 4369, "mtime": 1751705706949, "results": "43", "hashOfConfig": "32"}, {"size": 7736, "mtime": 1751706572396, "results": "44", "hashOfConfig": "32"}, {"size": 6315, "mtime": 1751691063359, "results": "45", "hashOfConfig": "32"}, {"size": 8933, "mtime": 1751698189900, "results": "46", "hashOfConfig": "32"}, {"size": 6892, "mtime": 1751706523599, "results": "47", "hashOfConfig": "32"}, {"size": 7928, "mtime": 1751706538622, "results": "48", "hashOfConfig": "32"}, {"size": 2631, "mtime": 1751691855369, "results": "49", "hashOfConfig": "32"}, {"size": 4197, "mtime": 1751698219580, "results": "50", "hashOfConfig": "32"}, {"size": 2288, "mtime": 1751696457432, "results": "51", "hashOfConfig": "32"}, {"size": 6833, "mtime": 1751690546091, "results": "52", "hashOfConfig": "32"}, {"size": 885, "mtime": 1751696440509, "results": "53", "hashOfConfig": "32"}, {"size": 6814, "mtime": 1751692521643, "results": "54", "hashOfConfig": "32"}, {"size": 4509, "mtime": 1751692491176, "results": "55", "hashOfConfig": "32"}, {"size": 7185, "mtime": 1751706598074, "results": "56", "hashOfConfig": "32"}, {"size": 1902, "mtime": 1751690107598, "results": "57", "hashOfConfig": "32"}, {"size": 7515, "mtime": 1751692615649, "results": "58", "hashOfConfig": "32"}, {"size": 2865, "mtime": 1751704669594, "results": "59", "hashOfConfig": "32"}, {"size": 6078, "mtime": 1751689552334, "results": "60", "hashOfConfig": "32"}, {"size": 4492, "mtime": 1751689524662, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wrrpjt", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts", [], []]