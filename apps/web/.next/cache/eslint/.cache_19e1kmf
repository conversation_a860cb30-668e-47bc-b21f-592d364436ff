[{"/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx": "1", "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx": "2", "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx": "3", "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx": "4", "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx": "5", "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx": "6", "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx": "7", "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx": "8", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx": "9", "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx": "10", "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx": "11", "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx": "12", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx": "13", "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx": "14", "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx": "15", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx": "16", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx": "17", "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx": "18", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx": "19", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx": "20", "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts": "21", "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts": "22", "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts": "23", "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts": "24", "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts": "25", "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts": "26", "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts": "27", "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx": "28", "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx": "29", "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts": "30", "/Users/<USER>/kesar_mango/apps/web/src/components/auth/CompactLoginForm.tsx": "31", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AuthPromptBanner.tsx": "32", "/Users/<USER>/kesar_mango/apps/web/src/components/ui/ContentWrapper.tsx": "33"}, {"size": 3137, "mtime": 1751699209137, "results": "34", "hashOfConfig": "35"}, {"size": 6040, "mtime": 1751705019819, "results": "36", "hashOfConfig": "35"}, {"size": 3943, "mtime": 1751723507088, "results": "37", "hashOfConfig": "35"}, {"size": 517, "mtime": 1751705033117, "results": "38", "hashOfConfig": "35"}, {"size": 397, "mtime": 1751699438674, "results": "39", "hashOfConfig": "35"}, {"size": 6509, "mtime": 1751704698962, "results": "40", "hashOfConfig": "35"}, {"size": 11299, "mtime": 1751689944143, "results": "41", "hashOfConfig": "35"}, {"size": 7623, "mtime": 1751691901763, "results": "42", "hashOfConfig": "35"}, {"size": 5747, "mtime": 1751698356220, "results": "43", "hashOfConfig": "35"}, {"size": 9576, "mtime": 1751704739543, "results": "44", "hashOfConfig": "35"}, {"size": 5542, "mtime": 1751707227033, "results": "45", "hashOfConfig": "35"}, {"size": 4540, "mtime": 1751723329946, "results": "46", "hashOfConfig": "35"}, {"size": 7969, "mtime": 1751723359621, "results": "47", "hashOfConfig": "35"}, {"size": 6315, "mtime": 1751691063359, "results": "48", "hashOfConfig": "35"}, {"size": 8926, "mtime": 1751723550526, "results": "49", "hashOfConfig": "35"}, {"size": 6887, "mtime": 1751707227043, "results": "50", "hashOfConfig": "35"}, {"size": 7923, "mtime": 1751707227043, "results": "51", "hashOfConfig": "35"}, {"size": 2631, "mtime": 1751691855369, "results": "52", "hashOfConfig": "35"}, {"size": 4197, "mtime": 1751698219580, "results": "53", "hashOfConfig": "35"}, {"size": 2288, "mtime": 1751696457432, "results": "54", "hashOfConfig": "35"}, {"size": 6833, "mtime": 1751690546091, "results": "55", "hashOfConfig": "35"}, {"size": 885, "mtime": 1751696440509, "results": "56", "hashOfConfig": "35"}, {"size": 6814, "mtime": 1751692521643, "results": "57", "hashOfConfig": "35"}, {"size": 4509, "mtime": 1751692491176, "results": "58", "hashOfConfig": "35"}, {"size": 7148, "mtime": 1751707227043, "results": "59", "hashOfConfig": "35"}, {"size": 1902, "mtime": 1751690107598, "results": "60", "hashOfConfig": "35"}, {"size": 7515, "mtime": 1751707227012, "results": "61", "hashOfConfig": "35"}, {"size": 2846, "mtime": 1751707331977, "results": "62", "hashOfConfig": "35"}, {"size": 6078, "mtime": 1751689552334, "results": "63", "hashOfConfig": "35"}, {"size": 4492, "mtime": 1751689524662, "results": "64", "hashOfConfig": "35"}, {"size": 4628, "mtime": 1751723307414, "results": "65", "hashOfConfig": "35"}, {"size": 1683, "mtime": 1751723434393, "results": "66", "hashOfConfig": "35"}, {"size": 888, "mtime": 1751723474455, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wrrpjt", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/kesar_mango/apps/web/src/app/layout.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/loading.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/page.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/providers.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/app/template.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/auth/LoginForm.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/betting/BettingSlip.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/casino/CasinoLobby.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/StatsOverview.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/dashboard/UserDashboard.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/error/ErrorBoundary.tsx", ["167"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/hero/AnimatedHero.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Header.tsx", ["168"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/layout/Sidebar.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/providers/LoadingProvider.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/LiveEvents.tsx", ["169"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/SportsGrid.tsx", ["170"], [], "/Users/<USER>/kesar_mango/apps/web/src/components/sports/TrendingBets.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AnimatedCounter.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/LoadingSpinner.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/data/mock-data.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/hooks/usePageLoading.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/api-config.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/env.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/security.ts", ["171"], [], "/Users/<USER>/kesar_mango/apps/web/src/lib/utils.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/services/data-service.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/auth-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/store/betting-store.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/types/index.ts", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/auth/CompactLoginForm.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/AuthPromptBanner.tsx", [], [], "/Users/<USER>/kesar_mango/apps/web/src/components/ui/ContentWrapper.tsx", [], [], {"ruleId": "172", "severity": 2, "message": "173", "line": 109, "column": 17, "nodeType": "174", "messageId": "175", "suggestions": "176"}, {"ruleId": "177", "severity": 1, "message": "178", "line": 126, "column": 23, "nodeType": "179", "endLine": 130, "endColumn": 25}, {"ruleId": "172", "severity": 2, "message": "173", "line": 66, "column": 33, "nodeType": "174", "messageId": "175", "suggestions": "180"}, {"ruleId": "172", "severity": 2, "message": "173", "line": 102, "column": 35, "nodeType": "174", "messageId": "175", "suggestions": "181"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 271, "column": 1, "nodeType": "184", "endLine": 283, "endColumn": 2}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["185", "186", "187", "188"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["189", "190", "191", "192"], ["193", "194", "195", "196"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"messageId": "197", "data": "198", "fix": "199", "desc": "200"}, {"messageId": "197", "data": "201", "fix": "202", "desc": "203"}, {"messageId": "197", "data": "204", "fix": "205", "desc": "206"}, {"messageId": "197", "data": "207", "fix": "208", "desc": "209"}, {"messageId": "197", "data": "210", "fix": "211", "desc": "200"}, {"messageId": "197", "data": "212", "fix": "213", "desc": "203"}, {"messageId": "197", "data": "214", "fix": "215", "desc": "206"}, {"messageId": "197", "data": "216", "fix": "217", "desc": "209"}, {"messageId": "197", "data": "218", "fix": "219", "desc": "200"}, {"messageId": "197", "data": "220", "fix": "221", "desc": "203"}, {"messageId": "197", "data": "222", "fix": "223", "desc": "206"}, {"messageId": "197", "data": "224", "fix": "225", "desc": "209"}, "replaceWithAlt", {"alt": "226"}, {"range": "227", "text": "228"}, "Replace with `&apos;`.", {"alt": "229"}, {"range": "230", "text": "231"}, "Replace with `&lsquo;`.", {"alt": "232"}, {"range": "233", "text": "234"}, "Replace with `&#39;`.", {"alt": "235"}, {"range": "236", "text": "237"}, "Replace with `&rsquo;`.", {"alt": "226"}, {"range": "238", "text": "239"}, {"alt": "229"}, {"range": "240", "text": "241"}, {"alt": "232"}, {"range": "242", "text": "243"}, {"alt": "235"}, {"range": "244", "text": "245"}, {"alt": "226"}, {"range": "246", "text": "239"}, {"alt": "229"}, {"range": "247", "text": "241"}, {"alt": "232"}, {"range": "248", "text": "243"}, {"alt": "235"}, {"range": "249", "text": "245"}, "&apos;", [3007, 3121], "\n              We&apos;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&lsquo;", [3007, 3121], "\n              We&lsquo;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&#39;", [3007, 3121], "\n              We&#39;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", "&rsquo;", [3007, 3121], "\n              We&rsquo;re sorry for the inconvenience. Our team has been notified and is working on a fix.\n            ", [2495, 2497], "&apos; ", [2495, 2497], "&lsquo; ", [2495, 2497], "&#39; ", [2495, 2497], "&rsquo; ", [3665, 3667], [3665, 3667], [3665, 3667], [3665, 3667]]