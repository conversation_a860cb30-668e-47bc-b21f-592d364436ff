"use strict";(()=>{var e={};e.id=820,e.ids=[820,888,660],e.modules={8445:(e,t,r)=>{r.r(t),r.d(t,{config:()=>c,default:()=>g,getServerSideProps:()=>P,getStaticPaths:()=>d,getStaticProps:()=>S,reportWebVitals:()=>b,routeModule:()=>f,unstable_getServerProps:()=>h,unstable_getServerSideProps:()=>x,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>m,unstable_getStaticProps:()=>_});var a=r(8877),s=r(4591),i=r(6021),l=r(5054),o=r.n(l),n=r(1445),u=r.n(n),p=r(8329);let g=(0,i.l)(p,"default"),S=(0,i.l)(p,"getStaticProps"),d=(0,i.l)(p,"getStaticPaths"),P=(0,i.l)(p,"getServerSideProps"),c=(0,i.l)(p,"config"),b=(0,i.l)(p,"reportWebVitals"),_=(0,i.l)(p,"unstable_getStaticProps"),m=(0,i.l)(p,"unstable_getStaticPaths"),v=(0,i.l)(p,"unstable_getStaticParams"),h=(0,i.l)(p,"unstable_getServerProps"),x=(0,i.l)(p,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:u(),Document:o()},userland:p})},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},1017:e=>{e.exports=require("path")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[592,216],()=>r(8445));module.exports=a})();